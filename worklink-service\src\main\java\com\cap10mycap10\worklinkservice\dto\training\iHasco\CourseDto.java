package com.cap10mycap10.worklinkservice.dto.training.iHasco;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.criteria.CriteriaBuilder;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CourseDto {

    private Integer access_code;
    private Long course_id;
    private String title;
    private String license_status;
    private Boolean on_hold;
    private List<String> languages_available;
    private Double pass_mark;
    private Integer refresh_months;


}
