package com.cap10mycap10.worklinkservice.implementation;


import com.cap10mycap10.worklinkservice.dao.LocationRepository;
import com.cap10mycap10.worklinkservice.dto.shiftlocation.ShiftLocationCreateDto;
import com.cap10mycap10.worklinkservice.dto.shiftlocation.ShiftLocationResultDto;
import com.cap10mycap10.worklinkservice.dto.shiftlocation.ShiftLocationUpdateDto;
import com.cap10mycap10.worklinkservice.mapper.shiftlocation.ShiftLocationDtoToShiftLocation;
import com.cap10mycap10.worklinkservice.mapper.shiftlocation.ShiftLocationToShiftLocationResultDto;
import com.cap10mycap10.worklinkservice.model.Location;
import com.cap10mycap10.worklinkservice.service.LocationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;

import javax.persistence.EntityNotFoundException;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class LocationServiceImpl implements LocationService {

    private final LocationRepository locationRepository;
    private final ShiftLocationToShiftLocationResultDto toShiftLocationResultDto;
    private final ShiftLocationDtoToShiftLocation toShiftLocation;

    public LocationServiceImpl(final LocationRepository locationRepository,
                               final ShiftLocationToShiftLocationResultDto toShiftLocationResultDto,
                               final ShiftLocationDtoToShiftLocation toShiftLocation) {
        this.locationRepository = locationRepository;
        this.toShiftLocationResultDto = toShiftLocationResultDto;
        this.toShiftLocation = toShiftLocation;
    }

    @Override
    public ShiftLocationResultDto save(ShiftLocationCreateDto shiftLocationCreateDto) {
        return toShiftLocationResultDto.convert(locationRepository.save(
                toShiftLocation.convert(shiftLocationCreateDto)
        ));
    }

    @Override
    public ShiftLocationResultDto findById(Long id) {
        return toShiftLocationResultDto.convert(locationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Shift location not found")));
    }

    @Override
    public List<ShiftLocationResultDto> findAll(Long agencyId) {
        return locationRepository.findAllFiltered(agencyId)
                .stream()
                .map(toShiftLocationResultDto::convert)
                .collect(Collectors.toList());
    }

    @Override
    public Page<ShiftLocationResultDto> findAllPaged(PageRequest of) {
        return locationRepository.findAllByOrderByCityAsc(of)
                .map(toShiftLocationResultDto::convert);
    }

    @Override
    public List<ShiftLocationResultDto> searchAllColumns(String of, Long agenycId) {
        return locationRepository.searchAllColumns(of, agenycId).stream()
                .map(toShiftLocationResultDto::convert).collect(Collectors.toList());
    }

    @Override
    public void deleteById(Long id) {
        Location location = getOne(id);
        try {
            locationRepository.deleteById(id);
            locationRepository.flush();
        } catch (Exception ex) {
            throw new BusinessValidationException("Shift Location cannot be deleted");
        }
    }

    @Override
    public ShiftLocationResultDto save(ShiftLocationUpdateDto shiftLocationUpdateDto) {
        throw new BusinessValidationException("Locations are managed from backend. Contact dev team for assistance");
    }

    @Override
    public Location getOne(Long id) {
        return locationRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Location not found"));
    }







}
