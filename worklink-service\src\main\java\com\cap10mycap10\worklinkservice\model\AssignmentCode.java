package com.cap10mycap10.worklinkservice.model;

import lombok.*;

import javax.persistence.*;
import java.util.*;


@Entity
@AllArgsConstructor
@NoArgsConstructor
@Data
@Table(
        uniqueConstraints = @UniqueConstraint(
                name = "uk_assignment_code_agency",
                columnNames = {
                        "name",
                        "code"
                }
        )
)
public class AssignmentCode extends AbstractAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = false)
    private String name;

    @Column(unique = true)
    private String code;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Services services;

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "assignmentCode", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<AssignmentRate> assignmentRates = new HashSet<>();

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        AssignmentCode that = (AssignmentCode) o;
        return Objects.equals(name, that.name) &&
                Objects.equals(code, that.code);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), name, code);
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("AssignmentCode{");
        sb.append("id=").append(id);
        sb.append(", city='").append(name).append('\'');
        sb.append(", code='").append(code).append('\'');
        sb.append(", services=").append(services);
        sb.append('}');
        return sb.toString();
    }
}
