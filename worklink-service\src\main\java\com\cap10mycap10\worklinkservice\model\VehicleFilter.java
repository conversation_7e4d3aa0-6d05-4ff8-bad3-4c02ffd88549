package com.cap10mycap10.worklinkservice.model;

import com.cap10mycap10.worklinkservice.enums.FuelType;
import com.cap10mycap10.worklinkservice.enums.Operator;
import com.cap10mycap10.worklinkservice.enums.VehicleType;
import lombok.*;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Entity
@Data
public class VehicleFilter {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @ManyToMany(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @JoinTable(name = "vehicle_filter_vehicles", joinColumns = {
            @JoinColumn(name = "filter_id", referencedColumnName = "id")}, inverseJoinColumns = {
            @JoinColumn(name = "vehicle_id", referencedColumnName = "id")})
    private Set<Vehicle> vehicles = new HashSet<>();
    @Enumerated(EnumType.STRING)
    private Operator vehicleOperator;

    private String names;
    @Enumerated(EnumType.STRING)
    private Operator nameOperator;

    private String models;
    @Enumerated(EnumType.STRING)
    private Operator modelOperator;

    private String colors;
    @Enumerated(EnumType.STRING)
    private Operator colorOperator;

    private String fuelTypes;
    @Enumerated(EnumType.STRING)
    private Operator fuelTypeOperator;


    private String types;
    @Enumerated(EnumType.STRING)
    private Operator typeOperator;


    @ManyToMany(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JoinTable(name = "vehicle_filter_locations", joinColumns = {
            @JoinColumn(name = "filter_id", referencedColumnName = "id")}, inverseJoinColumns = {
            @JoinColumn(name = "vehicle_id", referencedColumnName = "id")})
    private Set<Location> locations = new HashSet<>();
    private Operator locationOperator;


    private Long agencyId;


    @OneToOne
    @JoinColumn(name="promotion_id")
    @JsonIgnore
    private Promotion promotion;


    public void setVehicles(Set<Vehicle> vehicles) {
        this.vehicles = vehicles;
    }

    public void setLocations(Set<Location> locations) {
        this.locations = locations;
    }

    public List<String> getModels() {
        if(isNull(models)) return null;
        return !models.isEmpty() ? List.of(models.split(",")) : null;
    }

    public void setModels(List<String> models) {
        if(nonNull(models))this.models = String.join("," ,models);
    }
    public List<String> getColors() {
        return (nonNull(colors)&& !colors.isEmpty())? List.of(colors.split(",")) : null;
    }

    public void setColors(List<String> colors) {
        if(nonNull(colors))this.colors = String.join("," ,colors);
    }

    public List<String> getNames() {
        if(isNull(names)) return null;
        return !names.isEmpty() ? List.of(names.split(",")): null;
    }

    public void setNames(List<String> names) {
        if(nonNull(names))this.names = String.join("," ,names);
    }

    public List<FuelType> getFuelTypes() {
        if(isNull(fuelTypes)) return null;
        return !fuelTypes.isEmpty() ? Stream.of(fuelTypes.split(",")).map(FuelType::valueOf).collect(Collectors.toList()) : null;
    }

    public void setFuelTypes(List<String> fuelTypes) {
        if(nonNull(fuelTypes))this.fuelTypes = String.join("," ,fuelTypes);
    }



    public List<VehicleType> getTypes() {
        if(isNull(types)) return null;
        return !types.isEmpty() ? Stream.of(types.split(",")).map(VehicleType::valueOf).collect(Collectors.toList()) : null;
    }

    public void setTypes(List<String> types) {
        if(nonNull(types))this.types = String.join("," ,types);
    }

    @Override
    public String toString() {
        return "VehicleFilter{" +
                "id=" + id +
                ", vehicles=" + vehicles +
                ", vehicleOperator=" + vehicleOperator +
                ", names='" + names + '\'' +
                ", nameOperator=" + nameOperator +
                ", models='" + models + '\'' +
                ", modelOperator=" + modelOperator +
                ", colors='" + colors + '\'' +
                ", colorOperator=" + colorOperator +
                ", fuelTypes='" + fuelTypes + '\'' +
                ", fuelTypeOperator=" + fuelTypeOperator +
                ", types='" + types + '\'' +
                ", typeOperator=" + typeOperator +
                ", locations=" + locations +
                ", locationOperator=" + locationOperator +
                ", agencyId=" + agencyId +
                ", promotion=" + promotion +
                '}';
    }
}
