package com.cap10mycap10.worklinkservice.model;

import lombok.*;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalTime;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
//@Table(
//        uniqueConstraints = @UniqueConstraint(
//                city = "uk_assignment_code_agency_client",
//                columnNames = {
//                        "assignment_code_id",
//                        "agent_id",
//                        "client_id",
//                        "shift_type_id",
//                        "shift_directorate_id"
//                }
//        )
//)
public class AssignmentRate  extends AbstractAuditingEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private AssignmentCode assignmentCode;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Agency agent;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Client client;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private ShiftType shiftType;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private ShiftDirectorate shiftDirectorate;

    private DayOfWeek dayOfTheWeek;

    private LocalTime startTime;

    private LocalTime endTime;

    private BigDecimal clientRate;//Agency bill client per hour

    private BigDecimal privateRate;//

    private BigDecimal umbrellaRate;//Companies but individuals

    private BigDecimal payeRate;//to be paid to worker


    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Location location;
}
