package com.cap10mycap10.worklinkservice.model;


import com.cap10mycap10.worklinkservice.enums.PayAdviceStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.opencsv.bean.CsvBindByName;
import lombok.*;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static java.util.Objects.isNull;

@EqualsAndHashCode(callSuper = true)
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PayAdvice extends AbstractAuditingEntity {
    @CsvBindByName(column = "Ref")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long agentId;

    private Long workerId;

    @CsvBindByName
    private BigDecimal totalAmount;

    @CsvBindByName(column = "Amount")
    private BigDecimal paidAmount;


    @Column(length = 50)
    private String paymentRef;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate payAdviceDate;

    @CsvBindByName(column = "Status")
    private String status ;


    @Enumerated(EnumType.STRING)
    private PayAdviceStatus payAdviceStatus = PayAdviceStatus.UNPAID;

    private BigDecimal vatPercentage;

    private BigDecimal vatAmount;

    private String totalHrs;

    private BigDecimal subTotalAmount;

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(
            mappedBy = "payAdvice",
            cascade = CascadeType.ALL,
            orphanRemoval = true,
            fetch = FetchType.EAGER
    )
    private List<PayAdviceItem> payAdviceItems = new ArrayList<>();

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(
            mappedBy = "payAdvice",
            orphanRemoval = true
    )
    @JsonIgnore
    private List<ShiftExpenseClaim> shiftExpenseClaims = new ArrayList<>();


    public void addPayAdviceItem(PayAdviceItem payAdviceItem) {
        payAdviceItem.setPayAdvice(this);
        payAdviceItems.add(payAdviceItem);

    }

    public void removePayAdviceItem(PayAdviceItem payAdviceItem) {
        payAdviceItems.remove(payAdviceItem);
        payAdviceItem.setPayAdvice(null);
    }


    public void addPayAdviceItems(List<PayAdviceItem> payAdviceItems) {
        for (PayAdviceItem item : payAdviceItems
        ) {
            addPayAdviceItem(item);
        }
    }

    public BigDecimal getTotalAmount() {
        if (isNull(totalAmount)) {
            return null;
        }
        return totalAmount.setScale(2, RoundingMode.HALF_EVEN);
    }

    public BigDecimal getVatPercentage() {
        return vatPercentage;
    }

    public BigDecimal getVatAmount() {
        if (isNull(vatAmount)) {
            return null;
        }
        return vatAmount.setScale(2, RoundingMode.HALF_EVEN);
    }

    public BigDecimal getSubTotalAmount() {
        if (isNull(totalAmount)) {
            return null;
        }
        return subTotalAmount.setScale(2, RoundingMode.HALF_EVEN);
    }


    public List<PayAdviceItem> getPayAdviceItems() {
        return payAdviceItems;
    }

    public String getTotalHrs() {
        double tot = 0.0;

        for (int i = 0; i < payAdviceItems.size(); i++) {
            tot += payAdviceItems.get(i).getNumberOfHoursWorked();
        }

        return String.valueOf(tot);
    }
}


