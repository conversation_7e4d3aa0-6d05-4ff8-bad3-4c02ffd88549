//package com.cap10mycap10.worklinkservice.dto.transport;
//
//import com.cap10mycap10.worklinkservice.enums.*;
//import com.cap10mycap10.worklinkservice.model.TransportWorkerSpec;
//import com.fasterxml.jackson.annotation.JsonFormat;
//import lombok.Getter;
//import lombok.Setter;
//import javax.persistence.EnumType;
//import javax.persistence.Enumerated;
//import java.time.LocalDateTime;
//import java.util.List;
//import java.util.Set;
//
//
//@Getter
//@Setter
//public class TransportUpdateDto {
//
//    private Long id;
//
//    private Long clientId;
//
//    private Set<Long> transportLegibleAgencyIDs;
//
//    private Long agencyId;
//
//    private Long pickupLocationDirectorateId;
//
//    private String pickupLocationPostCode;
//
//    private String pickupLocationContactNumber;
//
//    private String destination;
//
//    private String destinationPostCode;
//
//    private String destinationContactNumber;
//
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
//    private LocalDateTime dateTimeRequired;
//
//    //    Passenger information
////    private String passengerName;
//
////    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
////    private LocalDate passengerDob;
//
//    private Integer passengerAge;
//
//    @Enumerated(EnumType.STRING)
//    private Gender passengerGender;
//
//    @Enumerated(EnumType.STRING)
//    private TransportStatus transportStatus;
//
////    private String passengerNHSNumber;
//
//    private Level assaultStaff;
//    private Level physicalAggression;
//    private Level verballyAggressive;
//    private Level selfHarm;
//    private Level absconsionRisk;
//    private Level sexuallyInappropriate;
//
//    private String reasonForTransport;
//
//    private String otherRisks;
//
//    private String mobilityIssues;
//
//    private String otherMobilityIssues;
//    private String bpostCode;
//
//    //    Escort service required
//    private String escortServiceRisk;
//
//    private Boolean isPassengerAwareOfTransport;
//    private Integer wardEscort;
//
//    private Boolean passengerRequiresRestraints;
//
//    private String reasonsForRestrains;
//
//    private String specialRequests;
//
//    private List<TransportWorkerSpec> transportWorkerSpecList;
//}
