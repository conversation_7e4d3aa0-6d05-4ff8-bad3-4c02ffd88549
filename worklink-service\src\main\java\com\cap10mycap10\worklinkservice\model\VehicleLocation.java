package com.cap10mycap10.worklinkservice.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.persistence.*;

/**
 * Junction entity representing the many-to-many relationship between Vehicle and Location.
 * This allows a single vehicle to be assigned to multiple locations.
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "vehicle_location")
@Data
public class VehicleLocation extends AbstractAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicle_id", nullable = false)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Vehicle vehicle;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "location_id", nullable = false)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Location location;

    /**
     * Indicates if this vehicle is currently active at this location.
     * This allows for soft deletion or temporary deactivation of vehicle-location assignments.
     */
    @Column(nullable = false)
    private Boolean active = true;

    /**
     * Optional priority for this vehicle at this location.
     * Higher numbers indicate higher priority for search results.
     */
    private Integer priority = 0;

    /**
     * Optional notes about this vehicle's assignment to this location.
     */
    @Column(length = 500)
    private String notes;

    // Constructors
    public VehicleLocation() {}

    public VehicleLocation(Vehicle vehicle, Location location) {
        this.vehicle = vehicle;
        this.location = location;
        this.active = true;
        this.priority = 0;
    }

    public VehicleLocation(Vehicle vehicle, Location location, Boolean active) {
        this.vehicle = vehicle;
        this.location = location;
        this.active = active;
        this.priority = 0;
    }
}
