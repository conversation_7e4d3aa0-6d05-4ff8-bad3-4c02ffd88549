package com.cap10mycap10.worklinkservice.model;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.*;
import java.time.ZoneId;
import java.util.HashSet;
import java.util.Set;

import static java.util.Objects.nonNull;

@EqualsAndHashCode(callSuper = true)
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
@Data
public class Location extends AbstractAuditingEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(nullable = false)
    private String city;
    private String cityAscii;
    private Float lng;
    private Float lat;
    @Column(nullable = false)
    private String country;
    private String iso3;
    private String iso2;
    @Column(nullable = false)
    private String capital;
    private String name;
    @Column(nullable = false)
    private String timeZone;

    @Enumerated(EnumType.STRING)
    private LocationType locationType;

    private String address;
    private String postalCode;
    private String state;
    private String description;

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @OneToMany(mappedBy = "location", fetch =  FetchType.LAZY)
    private Set<ShiftDirectorate> directorates = new HashSet<>();

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @OneToMany(mappedBy = "location", fetch = FetchType.LAZY)
    private Set<VehicleLocation> vehicleLocations = new HashSet<>();

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agency_id")
    private Agency agency;

    @Override
    public String toString(){
        return city+", "+country;
    }

    public ZoneId getTimeZoneId() {
//        log.info("The timezone is:{}", timeZone);
        return nonNull(timeZone)?ZoneId
                .of(
                        timeZone
                ):null;
    }




}
