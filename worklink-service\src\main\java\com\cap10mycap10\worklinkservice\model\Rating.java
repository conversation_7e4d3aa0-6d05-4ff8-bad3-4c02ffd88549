package com.cap10mycap10.worklinkservice.model;

import com.cap10mycap10.worklinkservice.enums.RatingType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import javax.persistence.*;
import java.util.HashSet;
import java.util.Set;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Rating extends AbstractAuditingEntity {

    @Id
    @JsonIgnore
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @OneToOne
    @EqualsAndHashCode.Exclude
    @JsonIgnore
    @ToString.Exclude
    private Transport transport;

    @OneToOne
    @EqualsAndHashCode.Exclude
    @JsonIgnore
    @ToString.Exclude
    private VehicleBooking vehicleBooking;

    @Enumerated(EnumType.STRING)
    private RatingType type;

//    @Transient
//    @JsonInclude
//    private String name;

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "rating", fetch =  FetchType.EAGER, cascade = CascadeType.ALL)
    private Set<RatingItem> ratingItems = new HashSet<>();



    private int rating;
    private String comment;

    public void setRatingItems(Set<RatingItem> ratingItems) {
        this.ratingItems = ratingItems;
        this.getRatingItems().forEach(e->e.setRating(this));
    }

//    public void setName(String name) {
//        this.name = this.vehicleBooking.getFirstname();
//    }

    public String getName() {
        return vehicleBooking.getFirstname();
    }
}
