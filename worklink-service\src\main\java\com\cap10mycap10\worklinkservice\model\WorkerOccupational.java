package com.cap10mycap10.worklinkservice.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.persistence.*;
import java.time.LocalDate;


@Entity
@AllArgsConstructor
@Data
public class WorkerOccupational {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String title;
    private String lastname;
    private String firstname;
    private String preferredName;
    private String nationality;
    private LocalDate dob;
    private String address;
    private String email;
    private String phone;

    private Boolean illness;
    private String illnessRes;
    private Boolean workIllness;
    private String workIllnessRes;
    private Boolean treatmentWait;
    private String treatmentWaitRes;
    private Boolean assistance;
    private String assistanceRes;
    private Boolean mrsa;
    private LocalDate mrsaDate;
    private Boolean cdif;
    private LocalDate cdifDate;

    private Boolean chickenPox;
    private LocalDate chickenPoxDate;
    private Boolean bbv;
    private LocalDate bbvDate;

    private Boolean travelled;
    private Boolean bcgTb;

    private Boolean cough;
    private Boolean wloss;
    private Boolean fever;
    private Boolean tb;
    private String tbRes;

    private LocalDate tripleVac;
    private LocalDate polio;
    private LocalDate tetanus;
    private LocalDate hepabc1;
    private LocalDate hepabc2;
    private LocalDate hepabc3;
    private LocalDate hepabb1;
    private LocalDate hepabb2;
    private LocalDate hepabb3;

    private Boolean varicella;
    private Boolean tbc;
    private Boolean rubella;
    private Boolean hepB;

    private Boolean hepBS;
    private Boolean hepC;
    private Boolean hiv;

    private Boolean exposure;
    private Boolean consent;
    private Boolean copy;

    private String signName;
    private Boolean signed;
    private String signDate;
    private Boolean submitted;

    @ManyToOne(fetch = FetchType.LAZY)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Worker worker;


}
