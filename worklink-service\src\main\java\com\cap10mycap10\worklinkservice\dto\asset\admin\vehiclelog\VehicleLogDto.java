package com.cap10mycap10.worklinkservice.dto.asset.admin.vehiclelog;
 
import com.cap10mycap10.worklinkservice.dto.transport.TransportDto;
import com.cap10mycap10.worklinkservice.enums.LogStatus;
import com.cap10mycap10.worklinkservice.enums.VehicleLogType;
import com.cap10mycap10.worklinkservice.mapper.transport.TransportToTransportDto;
import com.cap10mycap10.worklinkservice.model.VehicleLog;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.beans.factory.annotation.Autowired;

import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

import java.time.Duration;
import java.time.Instant;

import static java.util.Objects.nonNull;

@Data
@NoArgsConstructor
public class VehicleLogDto {
    public VehicleLogType type;
    public String approvedBy;
    public String approvedByName;
    public LogStatus status;
    public String worker;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    public Instant createdDate;
    public Long id;
    public Float startMileage;
    public Long totalMinutes;
    public TransportDto transport;
    public Float endMileage;
    public String vehicle;
    public Float totalMileage;
    public String notes;
    public String feedback;
    public String formComplete;
    public String damageReport;
    public String damageDoc;

    public Boolean indicators;
    public String comment;
    public Boolean drivingControls;
    public Boolean wheelCondition;
    public Boolean tyreInflation;
    public Boolean brakes;
    public Boolean windscreen;
    public String damageDescriptions;
    public Boolean mirrors;
    public Boolean speedometer;
    public Boolean battery;
    public Boolean fuel;
    public Boolean seatbelt;
    public Boolean doors;
    public Boolean oil;
    public Boolean engineCheckLight;
    public Boolean warningLight;
    public Boolean litter;
    public Boolean hardSurface;
    public Boolean seats;
    public Boolean equipment;
    public Boolean sanitizer;
    public Boolean cellArea;
    public Boolean lamp;
    public Boolean sideReapter;
    public Boolean stoplamp;
    public Boolean reflectors;
    public Boolean markers;
    public Boolean warningdevices;
    public Boolean mirror;
    public Boolean drivingcontrol;
    public Boolean body;
    public Boolean horn;
    public Boolean wipers;
    public Boolean washers;
    public Boolean fluidleaks;
    public Boolean exhaust;
    public Boolean coolant;
    public Boolean instrumentalPanel;
    public Boolean adblue;
    public Boolean trailercoupling;
    public Long vehicleId;
    public Long transportId;

    @ManyToOne(fetch = FetchType.LAZY)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    public VehicleLog vehicleLog;





    public VehicleLogDto(VehicleLog vehicleLog) {
        var vehicle = vehicleLog.getVehicle();
        this.id = vehicleLog.getId();
        this.startMileage = vehicleLog.getStartMileage();

        this.endMileage = vehicleLog.getEndMileage();
        this.comment = vehicleLog.getComment();
        this.vehicle = (nonNull(vehicle.getName())?vehicle.getName():"")+" "
                +(nonNull(vehicle.getModel())?vehicle.getModel():"")+" "
                +(nonNull(vehicle.getRegno())?vehicle.getRegno():"");
        this.totalMileage = vehicleLog.getTotalMileage();
        this.notes = vehicleLog.getNotes();
        this.feedback = vehicleLog.getFeedback();
        this.damageReport = vehicleLog.getDamageReport();
        this.damageDoc = vehicleLog.getDamageDoc();
        this.doors = vehicleLog.getDoors();
        this.seats = vehicleLog.getSeats();



        this.indicators = vehicleLog.getIndicators();
        this.drivingControls = vehicleLog.getDrivingControls();
        this.wheelCondition = vehicleLog.getWheelCondition();
        this.tyreInflation = vehicleLog.getTyreInflation();
        this.brakes = vehicleLog.getBrakes();
        this.windscreen = vehicleLog.getWindscreen();
        this.damageDescriptions = vehicleLog.getDamageDescriptions();
        this.mirrors = vehicleLog.getMirrors();
        this.speedometer = vehicleLog.getSpeedometer();
        this.battery = vehicleLog.getBattery();
        this.fuel = vehicleLog.getFuel();
        this.seatbelt = vehicleLog.getSeatbelt();
        this.doors = vehicleLog.getDoors();
        this.oil = vehicleLog.getOil();
        this.engineCheckLight = vehicleLog.getEngineCheckLight();
        this.warningLight = vehicleLog.getWarningLight();
        this.litter = vehicleLog.getLitter();
        this.hardSurface = vehicleLog.getHardSurface();
        this.seats = vehicleLog.getSeats();
        this.equipment = vehicleLog.getEquipment();
        this.sanitizer = vehicleLog.getSanitizer();
        this.cellArea = vehicleLog.getCellArea();
        this.lamp = vehicleLog.getLamp();
        this.sideReapter = vehicleLog.getSideReapter();
        this.stoplamp = vehicleLog.getStoplamp();
        this.reflectors = vehicleLog.getReflectors();
        this.markers = vehicleLog.getMarkers();
        this.warningdevices = vehicleLog.getWarningdevices();
        this.mirror = vehicleLog.getMirror();
        this.drivingcontrol = vehicleLog.getDrivingcontrol();
        this.body = vehicleLog.getBody();
        this.horn = vehicleLog.getHorn();
        this.wipers = vehicleLog.getWipers();
        this.washers = vehicleLog.getWashers();
        this.fluidleaks = vehicleLog.getFluidleaks();
        this.exhaust = vehicleLog.getExhaust();
        this.coolant = vehicleLog.getCoolant();
        this.instrumentalPanel = vehicleLog.getInstrumentalPanel();
        this.adblue = vehicleLog.getAdblue();
        this.trailercoupling = vehicleLog.getTrailercoupling();
        this.createdDate = vehicleLog.getCreatedDate();
        this.approvedBy = vehicleLog.getApprovedBy();
        this.type = vehicleLog.getType();
        this.approvedByName = vehicleLog.getApprovedByName();
        this.worker = vehicleLog.getWorker().getFirstname()+" "+vehicleLog.getWorker().getLastname();
        this.trailercoupling = vehicleLog.getTrailercoupling();
        this.vehicleId = vehicleLog.getVehicle().getId();
        if(nonNull(vehicleLog.getTransport()))
            this.transportId = vehicleLog.getTransport().getId();
    }
}
