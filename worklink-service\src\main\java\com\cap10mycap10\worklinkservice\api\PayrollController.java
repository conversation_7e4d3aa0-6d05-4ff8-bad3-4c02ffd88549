package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.payadvice.PayAdviceResult;
import com.cap10mycap10.worklinkservice.dto.payslip.PayslipResultDto;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import com.cap10mycap10.worklinkservice.model.Payslip;
import com.cap10mycap10.worklinkservice.service.PayAdviceService;
import com.cap10mycap10.worklinkservice.service.PayslipService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.transaction.Transactional;


@Slf4j
@RestController
@Transactional
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class PayrollController {

    private final PayslipService payslipService;
    private final PayAdviceService payAdviceService;

    public PayrollController(PayslipService payslipService, PayAdviceService payAdviceService) {
        this.payslipService = payslipService;
        this.payAdviceService = payAdviceService;
    }



    @PostMapping("/payslip-upload")
    public ResponseEntity<Payslip> uploadPayslip(@RequestParam("file") MultipartFile file,
                                                 @RequestParam("agencyId") Long agencyId,
                                                 @RequestParam("workerId") Long workerId
    ) {

        log.info("Request to get client invoices : {}");
        return ResponseEntity.ok(payslipService.addPayslip( agencyId, workerId, file));
    }

    @GetMapping("/worker-payslips/{workerId}/{page}/{size}")
    public    ResponseEntity<Page<PayslipResultDto>> getWorkerPayslips(@RequestParam("workerId") Long workerId,
                                                                       @PathVariable("page") int page,
                                                                       @PathVariable("size") int size
    ) {

        log.info("Request to get client invoices : {}");

        return ResponseEntity.ok(payslipService.findWorkerPayslips(workerId, PageRequest.of(page, size)));
    }
    @GetMapping("/worker-pay-advices/{workerId}/{page}/{size}")
    public    ResponseEntity<Page<PayAdviceResult>> getWorkerPayAdvices(@RequestParam("workerId") Long workerId,
                                                                  @PathVariable("page") int page,
                                                                  @PathVariable("size") int size
    ) {

        log.info("Request to get client invoices : {}");

        return ResponseEntity.ok(payAdviceService.findWorkerPayAdvices(workerId, PageRequest.of(page, size)));
    }

    @GetMapping(value = "payroll-workers/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<WorkerResultDto>> findAllAgencyWorkersforPaslip(@PathVariable("agencyId") Long agencyId,
                                                                               @PathVariable("page") int page,
                                                                               @PathVariable("size") int size) {
        log.info("Request to view agency workers with payslips : {}, {}", agencyId, page, size);
        return ResponseEntity.ok(payslipService.findAllWorkersPaylipPaged(agencyId, PageRequest.of(page, size)));
    }


    @GetMapping(value = "payroll-workers/search/{agencyId}/{query}/{page}/{size}")
    public ResponseEntity<Page<WorkerResultDto>> searchAllAgencyWorkersforPaslip(@PathVariable("agencyId") Long agencyId,
                                                                               @PathVariable("page") int page,
                                                                               @PathVariable("query") String query,
                                                                               @PathVariable("size") int size) {
        log.info("Request to view agency workers with payslips : {}, {}", agencyId, page, size);
        return ResponseEntity.ok(payslipService.searchAllWorkersPaylipPaged(agencyId, query, PageRequest.of(page, size)));
    }


}
