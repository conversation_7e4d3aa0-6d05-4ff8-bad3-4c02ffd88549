package com.cap10mycap10.worklinkservice;

import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest
@ActiveProfiles("test")
@EnableAutoConfiguration(exclude = {HibernateJpaAutoConfiguration.class})
public class ApplicationStartupTest {

    @Test
    public void contextLoads() {
        // This test will pass if the application context loads successfully
    }
}
