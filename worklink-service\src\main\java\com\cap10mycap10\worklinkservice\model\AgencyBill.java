package com.cap10mycap10.worklinkservice.model;


import com.cap10mycap10.worklinkservice.enums.BillStatus;
import lombok.*;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(uniqueConstraints={@UniqueConstraint(columnNames={"shift_id"})})
public class AgencyBill extends AbstractAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Date issueDate;

    private Date dueDate;

    private Date paidDate;

    private String totalUnits;

    private BigDecimal chargeRate;
    private BigDecimal vatRate;
    private BigDecimal totalCharge;
    private BigDecimal discountCharge;
    private BigDecimal subTotal;
    private BigDecimal totalDue;

    private Boolean paid;

    private String paymentRef;

    @Enumerated(EnumType.STRING)
    private BillStatus status;

    private String notes;

    @ManyToOne(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Agency agency;

    @ManyToOne(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Client client;

    @ManyToOne(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Worker worker;

    @OneToOne(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Shift shift;
}
