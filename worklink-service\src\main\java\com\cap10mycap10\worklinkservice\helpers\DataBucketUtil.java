package com.cap10mycap10.worklinkservice.helpers;


import com.cap10mycap10.worklinkservice.dto.file.FileDto;
import com.cap10mycap10.worklinkservice.enums.WorklinkUserType;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.GCPFileUploadException;
import com.cap10mycap10.worklinkservice.model.WorkerTraining;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.storage.Blob;
import com.google.cloud.storage.Bucket;
import com.google.cloud.storage.Storage;
import com.google.cloud.storage.StorageOptions;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.utility.RandomString;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class DataBucketUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataBucketUtil.class);

    @Value("${gcp.config.file}")
    private String gcpConfigFile;

    @Value("${gcp.project.id}")
    private String gcpProjectId;

    @Value("${gcp.bucket.id}")
    private String gcpBucketId;

    @Value("${gcp.dir.name}")
    private String gcpDirectoryName;


    public String uploadFile(MultipartFile files, String title, String userId, WorklinkUserType userType) {
        List<String> types = new ArrayList<String>();
        types.add("application/pdf");
        if (!types.contains(files.getContentType()))
            throw new BusinessValidationException("Uploaded file type is not supported. Please upload a pdf");
        if (files.isEmpty())
            throw new BusinessValidationException("Uploaded file is cannot be empty.");


        List<WorkerTraining> inputFiles = new ArrayList<>();
        String originalFileName = files.getOriginalFilename();
        if(originalFileName == null)
            throw new BusinessValidationException("Original file city is null");

        Path path = new File(originalFileName).toPath();
        String contentType;

        try {
            contentType = Files.probeContentType(path);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        FileDto fileDto = uploadFile(files, originalFileName, contentType);

        if (fileDto != null)
            return fileDto.getFileUrl();
        else
            throw new BusinessValidationException("File upload failed");
    }



    public FileDto uploadFile(MultipartFile multipartFile, String fileName, String contentType) {

        try{

            LOGGER.info("Start file uploading process on GCS");
            byte[] fileData = FileUtils.readFileToByteArray(convertFile(multipartFile));

            InputStream inputStream = new ClassPathResource(gcpConfigFile).getInputStream();

            StorageOptions options = StorageOptions.newBuilder().setProjectId(gcpProjectId)
                    .setCredentials(GoogleCredentials.fromStream(inputStream)).build();

            Storage storage = options.getService();
            Bucket bucket = storage.get(gcpBucketId,Storage.BucketGetOption.fields());

            RandomString id = new RandomString();
            Blob blob = bucket.create(gcpDirectoryName + "/" + id.nextString() + checkFileExtension(fileName), fileData, contentType);

            if(blob != null){
                LOGGER.debug("File successfully uploaded to GCS");
                return new FileDto(blob.getName(), blob.getMediaLink());
            }

        }catch (Exception e){
            LOGGER.error("An error occurred while uploading data. Exception: ", e);
            throw new GCPFileUploadException("An error occurred while storing data to GCS");
        }
        throw new GCPFileUploadException("An error occurred while storing data to GCS");
    }

    private File convertFile(MultipartFile file) {

        try{
            if(file.getOriginalFilename() == null){
                throw new BusinessValidationException("Original file city is null");
            }
            File convertedFile = new File(file.getOriginalFilename());
            FileOutputStream outputStream = new FileOutputStream(convertedFile);
            outputStream.write(file.getBytes());
            outputStream.close();
            LOGGER.debug("Converting multipart file : {}", convertedFile);
            return convertedFile;
        }catch (Exception e){
            throw new BusinessValidationException("An error has occurred while converting the file");
        }
    }

    private String checkFileExtension(String fileName) {
        log.info("Checking filename extension name is: "+fileName);
        if(fileName != null && fileName.contains(".")){
            String[] extensionList = {".png", ".jpeg", ".jpg", ".pdf", ".webp", ".doc", ".docx"};

            for(String extension: extensionList) {
                if (fileName.toLowerCase().endsWith(extension.toLowerCase())) {
                    LOGGER.debug("Accepted file type : {}", extension);
                    return extension;
                }
            }
        }
        LOGGER.error("Not a permitted file type");
        throw new BusinessValidationException("Not a permitted file type");
    }
}
