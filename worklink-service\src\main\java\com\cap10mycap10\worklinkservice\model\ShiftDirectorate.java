package com.cap10mycap10.worklinkservice.model;


import lombok.*;

import javax.persistence.*;

@EqualsAndHashCode(callSuper = true)
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShiftDirectorate  extends AbstractAuditingEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true)
    private Long deputyId;

    private String name;
    private String postCode;

    private String phoneNumber;

    @ManyToOne(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    Location location;

    @ManyToOne(fetch = FetchType.EAGER)

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    Client client;

}
