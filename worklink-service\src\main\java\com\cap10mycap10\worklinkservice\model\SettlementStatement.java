package com.cap10mycap10.worklinkservice.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.List;

@Entity
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SettlementStatement extends AbstractAuditingEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    private Agency agency;

    @OneToMany
    @JoinColumn(name = "settlement_id")
    private List<Invoice> invoices;

    @Column(nullable = false)
    private Double totalAmount;

    @Column(nullable = false)
    private LocalDate settlementDate;



    public SettlementStatement(Agency carRental, List<Invoice> invoices, Double totalAmount, LocalDate settlementDate) {
        Double totalSettlementAmount = invoices.stream()
                .mapToDouble(i-> i.getTotalAmount().doubleValue())
                .sum();
        this.agency = carRental;
        this.invoices = invoices;
        this.totalAmount = totalAmount;
        this.settlementDate = settlementDate;
    }

}
