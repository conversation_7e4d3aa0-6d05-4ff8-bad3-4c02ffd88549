package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.model.Shift;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import java.util.List;

@Component
public class ShiftCriteriaQuery {

    private final EntityManager entityManager;

    @Autowired
    public ShiftCriteriaQuery(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    public List<Shift> getShiftForClient(List<Long> clientList) {
                return entityManager
                .createQuery(
                        "select b " +
                                "from Shift b " +
                                "where b.client_id in (:payerId)", Shift.class)
                .setParameter(
                        "payerId",
                        clientList
                )
                .getResultList();
    }

}
