package com.cap10mycap10.worklinkservice.dto.notification;

import com.cap10mycap10.worklinkservice.enums.WorklinkUserType;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.Column;
import java.util.List;

@Data
public class NotificationCreateDto {
    @Column(length = 1000)
    private String body;
    private String title;
    private String token;
    private Long agencyId;
    private Long sendingAgencyId;
    private Long workerId;
    private Long sendingClientId;
    private MultipartFile file;

    private WorklinkUserType senderType;
    private WorklinkUserType sendToType;
    private Long senderId;
    private List<Long> sendToAssCodes;
    private Boolean sendToAll;
    private List<Long> sendToIds;

    public NotificationCreateDto(Long workerId, String title, String body){
        this.workerId = workerId;
        this.title = title;
        this.body = body;
    }

    public NotificationCreateDto(){
    }

}
