package com.cap10mycap10.worklinkservice.model;


import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;

@Embeddable
@AllArgsConstructor
@NoArgsConstructor
public class WorkerAgencyId implements Serializable {

    @Column(name = "agency_id")
    private Long agencyId;

    @Column(name = "worker_id")
    private Long workerId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WorkerAgencyId that = (WorkerAgencyId) o;
        return Objects.equals(agencyId, that.agencyId) && Objects.equals(workerId, that.workerId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(agencyId, workerId);
    }
}
