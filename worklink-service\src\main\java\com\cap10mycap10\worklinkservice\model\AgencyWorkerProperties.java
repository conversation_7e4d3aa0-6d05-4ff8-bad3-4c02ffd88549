package com.cap10mycap10.worklinkservice.model;

import com.cap10mycap10.worklinkservice.enums.RightToWork;
import com.cap10mycap10.worklinkservice.enums.WorkerStatus;
import lombok.*;

import javax.persistence.*;
import java.time.LocalDate;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table
public class AgencyWorkerProperties extends AbstractAuditingEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @ManyToOne(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Agency agency;
    @ManyToOne(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Worker worker;
    private Integer rating;
    private String paymentMethod;
    private LocalDate employmentStartDate;
    private LocalDate contractEndDate;
    private LocalDate nextCheckDate;
    @Enumerated(EnumType.STRING)
    private RightToWork rightToWork;
    private String dbsNumber;
    private LocalDate dbsExpiry;
    private LocalDate expiry;
    private String restrictions;
    private LocalDate restrictionExpiry;
    private Boolean eligible;
    private String proof;
    private String visa;
    private LocalDate visaExpiry;
    private LocalDate signDate;
    private Boolean paperwork;
    private String approver;
    private String position;
    private String comment;
    private String signed;
    private String paycycle;
    private String weekHrs;
    private String rtiId;
    private String startBasis;
    @Enumerated(EnumType.STRING)
    private WorkerStatus status;
}
