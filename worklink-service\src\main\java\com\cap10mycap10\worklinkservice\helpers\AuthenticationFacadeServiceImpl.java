package com.cap10mycap10.worklinkservice.helpers;

 import com.cap10mycap10.worklinkservice.model.Worker;
 import com.cap10mycap10.worklinkservice.service.AuthenticationFacadeService;
 import com.cap10mycap10.worklinkservice.service.WorkerService;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
 import org.springframework.security.core.userdetails.UserDetails;
 import org.springframework.stereotype.Component;

@Component
public class AuthenticationFacadeServiceImpl implements  AuthenticationFacadeService  {

    @Autowired
    WorkerService workerService;

 
    public Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }


 
    public String getAuthUsername() {

        Object principal = SecurityContextHolder
                .getContext()
                .getAuthentication()
                .getPrincipal();
        String username;
        if (principal instanceof UserDetails) {
            username = ((UserDetails) principal).getUsername();
        } else {
            username = principal.toString();
        }
        return username;
    }








}
