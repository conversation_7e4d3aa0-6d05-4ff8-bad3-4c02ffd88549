package com.cap10mycap10.worklinkservice.model;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.*;

import javax.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;

import static java.util.Objects.isNull;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonSerialize
public class PayAdviceItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long shiftId;

    private String dayOfTheWeek;

    private String startTime;

    private String endTime;

    private String startDate;

    private String endDate;
    private String directorate;

    private double numberOfHoursWorked;

    private BigDecimal rate;

    private BigDecimal total;

    @ManyToOne()
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private PayAdvice payAdvice;

    public BigDecimal getRate() {
        return rate;
    }

    public BigDecimal getTotal() {
        if (isNull(total)) {
            return null;
        }
        return total.setScale(2, RoundingMode.HALF_EVEN);
    }


    public PayAdvice getPayAdvice() {
        return null;
    }
}
