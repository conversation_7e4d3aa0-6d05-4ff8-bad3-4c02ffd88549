package com.cap10mycap10.worklinkservice.dto.workertrainingsession;

import com.cap10mycap10.worklinkservice.enums.TrainingStatus;
import com.cap10mycap10.worklinkservice.enums.WorkerTrainingSessionStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkerTrainingSessionResultsDto {

    private Long id;

    private String workerName;

    private Long trainingId;

    private String trainingName;

    private String trainerName;

    private Long trainerId;

    private String address;

    private String postCode;
    private double breakTime;

    private String agencyName;
    private boolean showCertificate;

    private String trainingSessionName;

    private String shiftLocationName;

    @Enumerated(EnumType.STRING)
    private WorkerTrainingSessionStatus trainingStatus;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm")
    private LocalDateTime startDateTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy")
    private LocalDate dateUploaded;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm")
    private LocalDateTime endDateTime;

    private String notes;

    private Double costPerTrainee;

    private Boolean skippedTraining;

    private Integer trainingScore;

    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="dd/MM/yyyy", timezone="Z")
    private LocalDate trainingExpiryDate;

    private Boolean passedTraining;

    private Boolean isAgencyPaying;
}
