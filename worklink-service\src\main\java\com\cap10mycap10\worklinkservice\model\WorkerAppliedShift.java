package com.cap10mycap10.worklinkservice.model;


import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.fasterxml.jackson.annotation.JsonBackReference;
import lombok.*;

import javax.persistence.*;
import java.time.LocalDate;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(uniqueConstraints = @UniqueConstraint(name = "uk_id_account_type", columnNames = {"worker_id", "shift_id"}))
public class WorkerAppliedShift {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonBackReference
    @JoinColumn(nullable = false)
    private Worker worker;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonBackReference
    @JoinColumn(nullable = false)
    private Shift shift;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonBackReference
    @JoinColumn(nullable = false)
    private Agency agency;

    private LocalDate appliedDate;
    @Enumerated(EnumType.STRING)
    private ShiftStatus shiftStatus;


}
