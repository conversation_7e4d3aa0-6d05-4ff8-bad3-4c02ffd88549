package com.cap10mycap10.worklinkservice.model;

import com.cap10mycap10.worklinkservice.enums.RatingItemType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import javax.persistence.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RatingItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING)
    private RatingItemType status;

    private int rate;

    @ManyToOne(fetch = FetchType.LAZY)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private Rating rating;

    public void setRate(int rate) {
        if(rate>5) this.rate =5;
        else this.rate = Math.max(rate, 0);
    }
}
